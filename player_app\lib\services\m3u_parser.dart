import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/models.dart';

class M3UParser {
  static const String _extinfTag = '#EXTINF:';
  static const String _extm3uTag = '#EXTM3U';

  /// Parse M3U content from a URL
  static Future<Playlist> parseFromUrl(String url, String playlistName) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final content = utf8.decode(response.bodyBytes);
        return _parseContent(content, playlistName, url);
      } else {
        throw Exception('Failed to load M3U playlist: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error parsing M3U from URL: $e');
    }
  }

  /// Parse M3U content from a string
  static Playlist parseFromString(String content, String playlistName) {
    return _parseContent(content, playlistName, null);
  }

  /// Parse M3U content from a local file
  static Future<Playlist> parseFromFile(File file, String playlistName) async {
    try {
      final content = await file.readAsString();
      return _parseContent(content, playlistName, file.path);
    } catch (e) {
      throw Exception('Error reading M3U file: $e');
    }
  }

  /// Internal method to parse M3U content
  static Playlist _parseContent(String content, String playlistName, String? url) {
    final lines = content.split('\n').map((line) => line.trim()).toList();
    final channels = <Channel>[];
    
    if (lines.isEmpty || !lines.first.startsWith(_extm3uTag)) {
      throw Exception('Invalid M3U format: Missing #EXTM3U header');
    }

    String? currentExtinf;
    
    for (int i = 1; i < lines.length; i++) {
      final line = lines[i];
      
      if (line.isEmpty || line.startsWith('#')) {
        if (line.startsWith(_extinfTag)) {
          currentExtinf = line;
        }
        continue;
      }
      
      // This should be a URL line
      if (currentExtinf != null && line.isNotEmpty) {
        final channel = _parseChannel(currentExtinf, line);
        if (channel != null) {
          channels.add(channel);
        }
        currentExtinf = null;
      }
    }

    return Playlist(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: playlistName,
      url: url,
      type: PlaylistType.m3u,
      channels: channels,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Parse individual channel from EXTINF line and URL
  static Channel? _parseChannel(String extinfLine, String url) {
    try {
      // Remove #EXTINF: prefix
      final info = extinfLine.substring(_extinfTag.length);
      
      // Split by comma to separate duration and title
      final parts = info.split(',');
      if (parts.length < 2) return null;
      
      final titlePart = parts.sublist(1).join(',').trim();
      final attributes = _parseAttributes(parts[0]);
      
      // Extract channel name (everything after the last comma or the whole title)
      String channelName = titlePart;
      if (titlePart.contains(',')) {
        channelName = titlePart.split(',').last.trim();
      }
      
      return Channel(
        id: url.hashCode.toString(),
        name: channelName.isNotEmpty ? channelName : 'Unknown Channel',
        url: url,
        logo: attributes['tvg-logo'],
        group: attributes['group-title'],
        description: attributes['tvg-name'] ?? channelName,
        type: ChannelType.live,
      );
    } catch (e) {
      print('Error parsing channel: $e');
      return null;
    }
  }

  /// Parse attributes from the EXTINF line
  static Map<String, String> _parseAttributes(String attributeString) {
    final attributes = <String, String>{};
    
    // Regular expression to match key="value" or key=value patterns
    final regex = RegExp(r'(\w+(?:-\w+)*)=(?:"([^"]*)"|([^\s]+))');
    final matches = regex.allMatches(attributeString);
    
    for (final match in matches) {
      final key = match.group(1);
      final value = match.group(2) ?? match.group(3);
      if (key != null && value != null) {
        attributes[key] = value;
      }
    }
    
    return attributes;
  }

  /// Validate M3U URL format
  static bool isValidM3UUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && 
             (uri.scheme == 'http' || uri.scheme == 'https') &&
             (url.toLowerCase().endsWith('.m3u') || 
              url.toLowerCase().endsWith('.m3u8') ||
              url.toLowerCase().contains('m3u'));
    } catch (e) {
      return false;
    }
  }

  /// Get playlist info without parsing all channels (for preview)
  static Future<Map<String, dynamic>> getPlaylistInfo(String url) async {
    try {
      final response = await http.head(Uri.parse(url));
      final contentLength = response.headers['content-length'];
      final contentType = response.headers['content-type'];
      
      return {
        'url': url,
        'contentLength': contentLength != null ? int.tryParse(contentLength) : null,
        'contentType': contentType,
        'isValid': response.statusCode == 200,
      };
    } catch (e) {
      return {
        'url': url,
        'isValid': false,
        'error': e.toString(),
      };
    }
  }
}
