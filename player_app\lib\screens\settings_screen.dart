import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/storage_service.dart';
import 'xtream_config_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _autoPlay = true;
  bool _showChannelLogos = true;
  double _bufferSize = 5.0;
  String _videoQuality = 'Auto';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await StorageService.getAppSettings();
      setState(() {
        _autoPlay = settings.autoPlay;
        _showChannelLogos = settings.showChannelLogos;
        _bufferSize = settings.bufferSize;
        _videoQuality = settings.videoQuality;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    return Scaffold(
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSection(
            'Playback Settings',
            [
              SwitchListTile(
                title: const Text('Auto Play'),
                subtitle: const Text('Automatically start playing when channel is selected'),
                value: _autoPlay,
                onChanged: (value) async {
                  setState(() {
                    _autoPlay = value;
                  });
                  await StorageService.setAutoPlay(value);
                },
              ),
              ListTile(
                title: const Text('Video Quality'),
                subtitle: Text(_videoQuality),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: _showVideoQualityDialog,
              ),
              ListTile(
                title: const Text('Buffer Size'),
                subtitle: Text('${_bufferSize.toInt()} seconds'),
                trailing: SizedBox(
                  width: 100,
                  child: Slider(
                    value: _bufferSize,
                    min: 1,
                    max: 30,
                    divisions: 29,
                    onChanged: (value) async {
                      setState(() {
                        _bufferSize = value;
                      });
                      await StorageService.setBufferSize(value);
                    },
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            'Display Settings',
            [
              SwitchListTile(
                title: const Text('Show Channel Logos'),
                subtitle: const Text('Display channel logos in the channel list'),
                value: _showChannelLogos,
                onChanged: (value) async {
                  setState(() {
                    _showChannelLogos = value;
                  });
                  await StorageService.setShowChannelLogos(value);
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            'Xtream Codes Settings',
            [
              ListTile(
                title: const Text('Manage Xtream Accounts'),
                subtitle: const Text('Add or edit Xtream Codes accounts'),
                leading: const Icon(Icons.account_circle),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: _showXtreamAccountsDialog,
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            'About',
            [
              ListTile(
                title: const Text('Version'),
                subtitle: const Text('1.0.0'),
                leading: const Icon(Icons.info),
              ),
              ListTile(
                title: const Text('Support'),
                subtitle: const Text('Get help and report issues'),
                leading: const Icon(Icons.help),
                trailing: const Icon(Icons.open_in_new),
                onTap: _openSupport,
              ),
              ListTile(
                title: const Text('Privacy Policy'),
                leading: const Icon(Icons.privacy_tip),
                trailing: const Icon(Icons.open_in_new),
                onTap: _openPrivacyPolicy,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        Card(
          child: Column(children: children),
        ),
      ],
    );
  }

  void _showVideoQualityDialog() {
    final qualities = ['Auto', '1080p', '720p', '480p', '360p'];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Video Quality'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: qualities.map((quality) {
            return RadioListTile<String>(
              title: Text(quality),
              value: quality,
              groupValue: _videoQuality,
              onChanged: (value) {
                setState(() {
                  _videoQuality = value!;
                });
                StorageService.setVideoQuality(value!);
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showXtreamAccountsDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const XtreamConfigScreen(),
      ),
    );
  }

  void _openSupport() async {
    const url = 'mailto:<EMAIL>';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  void _openPrivacyPolicy() async {
    const url = 'https://example.com/privacy';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
}
