import 'channel.dart';

class Playlist {
  final String id;
  final String name;
  final String? url;
  final PlaylistType type;
  final List<Channel> channels;
  final DateTime createdAt;
  final DateTime updatedAt;

  Playlist({
    required this.id,
    required this.name,
    this.url,
    required this.type,
    required this.channels,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Playlist.fromJson(Map<String, dynamic> json) {
    return Playlist(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      url: json['url'],
      type: _parsePlaylistType(json['type']),
      channels: (json['channels'] as List<dynamic>?)
              ?.map((channel) => Channel.fromJson(channel))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type.toString(),
      'channels': channels.map((channel) => channel.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  static PlaylistType _parsePlaylistType(dynamic type) {
    if (type == null) return PlaylistType.m3u;
    switch (type.toString().toLowerCase()) {
      case 'xtream':
        return PlaylistType.xtream;
      case 'm3u':
      default:
        return PlaylistType.m3u;
    }
  }

  Playlist copyWith({
    String? id,
    String? name,
    String? url,
    PlaylistType? type,
    List<Channel>? channels,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      type: type ?? this.type,
      channels: channels ?? this.channels,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Playlist(id: $id, name: $name, type: $type, channels: ${channels.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Playlist && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum PlaylistType {
  m3u,
  xtream,
}
