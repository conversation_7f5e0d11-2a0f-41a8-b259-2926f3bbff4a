class Channel {
  final String id;
  final String name;
  final String url;
  final String? logo;
  final String? group;
  final String? description;
  final ChannelType type;

  Channel({
    required this.id,
    required this.name,
    required this.url,
    this.logo,
    this.group,
    this.description,
    this.type = ChannelType.live,
  });

  factory Channel.fromJson(Map<String, dynamic> json) {
    return Channel(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      logo: json['logo'],
      group: json['group'],
      description: json['description'],
      type: _parseChannelType(json['type']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'logo': logo,
      'group': group,
      'description': description,
      'type': type.toString(),
    };
  }

  static ChannelType _parseChannelType(dynamic type) {
    if (type == null) return ChannelType.live;
    switch (type.toString().toLowerCase()) {
      case 'movie':
        return ChannelType.movie;
      case 'series':
        return ChannelType.series;
      case 'live':
      default:
        return ChannelType.live;
    }
  }

  @override
  String toString() {
    return 'Channel(id: $id, name: $name, url: $url, group: $group)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Channel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum ChannelType {
  live,
  movie,
  series,
}
