import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

class StorageService {
  static const String _playlistsKey = 'playlists';
  static const String _xtreamConfigsKey = 'xtream_configs';
  static const String _settingsKey = 'app_settings';

  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Playlist management
  static Future<List<Playlist>> getPlaylists() async {
    await init();
    final playlistsJson = _prefs!.getStringList(_playlistsKey) ?? [];
    return playlistsJson
        .map((json) => Playlist.fromJson(jsonDecode(json)))
        .toList();
  }

  static Future<void> savePlaylists(List<Playlist> playlists) async {
    await init();
    final playlistsJson = playlists
        .map((playlist) => jsonEncode(playlist.toJson()))
        .toList();
    await _prefs!.setStringList(_playlists<PERSON><PERSON>, playlists<PERSON><PERSON>);
  }

  static Future<void> addPlaylist(Playlist playlist) async {
    final playlists = await getPlaylists();
    playlists.add(playlist);
    await savePlaylists(playlists);
  }

  static Future<void> removePlaylist(String playlistId) async {
    final playlists = await getPlaylists();
    playlists.removeWhere((playlist) => playlist.id == playlistId);
    await savePlaylists(playlists);
  }

  static Future<void> updatePlaylist(Playlist updatedPlaylist) async {
    final playlists = await getPlaylists();
    final index = playlists.indexWhere((p) => p.id == updatedPlaylist.id);
    if (index != -1) {
      playlists[index] = updatedPlaylist;
      await savePlaylists(playlists);
    }
  }

  // Xtream Codes configuration management
  static Future<List<XtreamConfig>> getXtreamConfigs() async {
    await init();
    final configsJson = _prefs!.getStringList(_xtreamConfigsKey) ?? [];
    return configsJson
        .map((json) => XtreamConfig.fromJson(jsonDecode(json)))
        .toList();
  }

  static Future<void> saveXtreamConfigs(List<XtreamConfig> configs) async {
    await init();
    final configsJson = configs
        .map((config) => jsonEncode(config.toJson()))
        .toList();
    await _prefs!.setStringList(_xtreamConfigsKey, configsJson);
  }

  static Future<void> addXtreamConfig(XtreamConfig config) async {
    final configs = await getXtreamConfigs();
    configs.add(config);
    await saveXtreamConfigs(configs);
  }

  static Future<void> removeXtreamConfig(String configId) async {
    final configs = await getXtreamConfigs();
    configs.removeWhere((config) => config.id == configId);
    await saveXtreamConfigs(configs);
  }

  static Future<void> updateXtreamConfig(XtreamConfig updatedConfig) async {
    final configs = await getXtreamConfigs();
    final index = configs.indexWhere((c) => c.id == updatedConfig.id);
    if (index != -1) {
      configs[index] = updatedConfig;
      await saveXtreamConfigs(configs);
    }
  }

  // App settings management
  static Future<AppSettings> getAppSettings() async {
    await init();
    final settingsJson = _prefs!.getString(_settingsKey);
    if (settingsJson != null) {
      return AppSettings.fromJson(jsonDecode(settingsJson));
    }
    return AppSettings.defaultSettings();
  }

  static Future<void> saveAppSettings(AppSettings settings) async {
    await init();
    await _prefs!.setString(_settingsKey, jsonEncode(settings.toJson()));
  }

  // Individual setting getters/setters for convenience
  static Future<bool> getAutoPlay() async {
    final settings = await getAppSettings();
    return settings.autoPlay;
  }

  static Future<void> setAutoPlay(bool value) async {
    final settings = await getAppSettings();
    await saveAppSettings(settings.copyWith(autoPlay: value));
  }

  static Future<bool> getShowChannelLogos() async {
    final settings = await getAppSettings();
    return settings.showChannelLogos;
  }

  static Future<void> setShowChannelLogos(bool value) async {
    final settings = await getAppSettings();
    await saveAppSettings(settings.copyWith(showChannelLogos: value));
  }

  static Future<double> getBufferSize() async {
    final settings = await getAppSettings();
    return settings.bufferSize;
  }

  static Future<void> setBufferSize(double value) async {
    final settings = await getAppSettings();
    await saveAppSettings(settings.copyWith(bufferSize: value));
  }

  static Future<String> getVideoQuality() async {
    final settings = await getAppSettings();
    return settings.videoQuality;
  }

  static Future<void> setVideoQuality(String value) async {
    final settings = await getAppSettings();
    await saveAppSettings(settings.copyWith(videoQuality: value));
  }

  // Clear all data
  static Future<void> clearAllData() async {
    await init();
    await _prefs!.clear();
  }
}

class AppSettings {
  final bool autoPlay;
  final bool showChannelLogos;
  final double bufferSize;
  final String videoQuality;

  const AppSettings({
    required this.autoPlay,
    required this.showChannelLogos,
    required this.bufferSize,
    required this.videoQuality,
  });

  factory AppSettings.defaultSettings() {
    return const AppSettings(
      autoPlay: true,
      showChannelLogos: true,
      bufferSize: 5.0,
      videoQuality: 'Auto',
    );
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      autoPlay: json['autoPlay'] ?? true,
      showChannelLogos: json['showChannelLogos'] ?? true,
      bufferSize: (json['bufferSize'] ?? 5.0).toDouble(),
      videoQuality: json['videoQuality'] ?? 'Auto',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoPlay': autoPlay,
      'showChannelLogos': showChannelLogos,
      'bufferSize': bufferSize,
      'videoQuality': videoQuality,
    };
  }

  AppSettings copyWith({
    bool? autoPlay,
    bool? showChannelLogos,
    double? bufferSize,
    String? videoQuality,
  }) {
    return AppSettings(
      autoPlay: autoPlay ?? this.autoPlay,
      showChannelLogos: showChannelLogos ?? this.showChannelLogos,
      bufferSize: bufferSize ?? this.bufferSize,
      videoQuality: videoQuality ?? this.videoQuality,
    );
  }
}
