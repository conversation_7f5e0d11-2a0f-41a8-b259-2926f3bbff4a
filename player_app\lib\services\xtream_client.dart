import 'package:dio/dio.dart';
import '../models/models.dart';

class XtreamClient {
  final Dio _dio;
  final XtreamConfig config;

  XtreamClient(this.config) : _dio = Dio() {
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  /// Test authentication with the Xtream server
  Future<bool> testAuthentication() async {
    try {
      final response = await _dio.get(
        config.baseApiUrl,
        queryParameters: {
          ...config.authParams,
          'action': 'get_server_info',
        },
      );

      return response.statusCode == 200 && response.data != null;
    } catch (e) {
      print('Authentication test failed: $e');
      return false;
    }
  }

  /// Get server information
  Future<Map<String, dynamic>?> getServerInfo() async {
    try {
      final response = await _dio.get(
        config.baseApiUrl,
        queryParameters: {
          ...config.authParams,
          'action': 'get_server_info',
        },
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>?;
      }
      return null;
    } catch (e) {
      print('Error getting server info: $e');
      return null;
    }
  }

  /// Get live TV channels
  Future<List<Channel>> getLiveChannels() async {
    try {
      final response = await _dio.get(
        config.baseApiUrl,
        queryParameters: {
          ...config.authParams,
          'action': 'get_live_streams',
        },
      );

      if (response.statusCode == 200 && response.data is List) {
        final channels = <Channel>[];
        for (final item in response.data as List) {
          if (item is Map<String, dynamic>) {
            final channel = _parseXtreamChannel(item, ChannelType.live);
            if (channel != null) {
              channels.add(channel);
            }
          }
        }
        return channels;
      }
      return [];
    } catch (e) {
      print('Error getting live channels: $e');
      return [];
    }
  }

  /// Get VOD (Video on Demand) movies
  Future<List<Channel>> getMovies() async {
    try {
      final response = await _dio.get(
        config.baseApiUrl,
        queryParameters: {
          ...config.authParams,
          'action': 'get_vod_streams',
        },
      );

      if (response.statusCode == 200 && response.data is List) {
        final channels = <Channel>[];
        for (final item in response.data as List) {
          if (item is Map<String, dynamic>) {
            final channel = _parseXtreamChannel(item, ChannelType.movie);
            if (channel != null) {
              channels.add(channel);
            }
          }
        }
        return channels;
      }
      return [];
    } catch (e) {
      print('Error getting movies: $e');
      return [];
    }
  }

  /// Get TV series
  Future<List<Channel>> getSeries() async {
    try {
      final response = await _dio.get(
        config.baseApiUrl,
        queryParameters: {
          ...config.authParams,
          'action': 'get_series',
        },
      );

      if (response.statusCode == 200 && response.data is List) {
        final channels = <Channel>[];
        for (final item in response.data as List) {
          if (item is Map<String, dynamic>) {
            final channel = _parseXtreamChannel(item, ChannelType.series);
            if (channel != null) {
              channels.add(channel);
            }
          }
        }
        return channels;
      }
      return [];
    } catch (e) {
      print('Error getting series: $e');
      return [];
    }
  }

  /// Get live TV categories
  Future<List<Map<String, dynamic>>> getLiveCategories() async {
    try {
      final response = await _dio.get(
        config.baseApiUrl,
        queryParameters: {
          ...config.authParams,
          'action': 'get_live_categories',
        },
      );

      if (response.statusCode == 200 && response.data is List) {
        return List<Map<String, dynamic>>.from(response.data);
      }
      return [];
    } catch (e) {
      print('Error getting live categories: $e');
      return [];
    }
  }

  /// Get all channels (live, movies, series) as a playlist
  Future<Playlist> getAllChannelsAsPlaylist() async {
    try {
      final liveChannels = await getLiveChannels();
      final movies = await getMovies();
      final series = await getSeries();

      final allChannels = [...liveChannels, ...movies, ...series];

      return Playlist(
        id: config.id,
        name: config.name,
        url: config.serverUrl,
        type: PlaylistType.xtream,
        channels: allChannels,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      print('Error getting all channels: $e');
      return Playlist(
        id: config.id,
        name: config.name,
        url: config.serverUrl,
        type: PlaylistType.xtream,
        channels: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  /// Parse Xtream channel data
  Channel? _parseXtreamChannel(Map<String, dynamic> data, ChannelType type) {
    try {
      final streamId = data['stream_id']?.toString() ?? data['series_id']?.toString();
      if (streamId == null) return null;

      String streamUrl;
      switch (type) {
        case ChannelType.live:
          streamUrl = '${config.serverUrl}/live/${config.username}/${config.password}/$streamId.ts';
          break;
        case ChannelType.movie:
          streamUrl = '${config.serverUrl}/movie/${config.username}/${config.password}/$streamId.mp4';
          break;
        case ChannelType.series:
          streamUrl = '${config.serverUrl}/series/${config.username}/${config.password}/$streamId.mp4';
          break;
      }

      return Channel(
        id: streamId,
        name: data['name']?.toString() ?? 'Unknown',
        url: streamUrl,
        logo: data['stream_icon']?.toString(),
        group: data['category_name']?.toString(),
        description: data['plot']?.toString() ?? data['name']?.toString(),
        type: type,
      );
    } catch (e) {
      print('Error parsing Xtream channel: $e');
      return null;
    }
  }

  /// Dispose resources
  void dispose() {
    _dio.close();
  }
}
