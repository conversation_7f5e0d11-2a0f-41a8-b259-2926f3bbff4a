import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/services.dart';
import 'playlist_screen.dart';
import 'settings_screen.dart';
import 'video_player_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  final List<Playlist> _playlists = [];

  final List<Widget> _screens = [];

  @override
  void initState() {
    super.initState();
    _loadPlaylists();
    _screens.addAll([
      PlaylistScreen(playlists: _playlists, onPlaylistTap: _onPlaylistTap),
      const SettingsScreen(),
    ]);
  }

  Future<void> _loadPlaylists() async {
    try {
      final playlists = await StorageService.getPlaylists();
      setState(() {
        _playlists.clear();
        _playlists.addAll(playlists);
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading playlists: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onPlaylistTap(Playlist playlist) {
    // Navigate to channel list or player
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChannelListScreen(playlist: playlist),
      ),
    );
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('IPTV Player'),
        actions: [
          if (_selectedIndex == 0)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showAddPlaylistDialog,
            ),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onBottomNavTap,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.playlist_play),
            label: 'Playlists',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
      floatingActionButton: _selectedIndex == 0
          ? FloatingActionButton(
              onPressed: _showAddPlaylistDialog,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  void _showAddPlaylistDialog() {
    showDialog(
      context: context,
      builder: (context) => AddPlaylistDialog(
        onPlaylistAdded: (playlist) {
          setState(() {
            _playlists.add(playlist);
          });
        },
      ),
    );
  }
}

class AddPlaylistDialog extends StatefulWidget {
  final Function(Playlist) onPlaylistAdded;

  const AddPlaylistDialog({super.key, required this.onPlaylistAdded});

  @override
  State<AddPlaylistDialog> createState() => _AddPlaylistDialogState();
}

class _AddPlaylistDialogState extends State<AddPlaylistDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _urlController = TextEditingController();
  PlaylistType _selectedType = PlaylistType.m3u;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Playlist'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Playlist Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a playlist name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: 'URL',
                border: OutlineInputBorder(),
                hintText: 'http://example.com/playlist.m3u',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a URL';
                }
                if (!Uri.tryParse(value)!.hasScheme) {
                  return 'Please enter a valid URL';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<PlaylistType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Playlist Type',
                border: OutlineInputBorder(),
              ),
              items: PlaylistType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.name.toUpperCase()),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _addPlaylist,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Add'),
        ),
      ],
    );
  }

  Future<void> _addPlaylist() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      Playlist playlist;
      if (_selectedType == PlaylistType.m3u) {
        playlist = await M3UParser.parseFromUrl(
          _urlController.text,
          _nameController.text,
        );
      } else {
        // For Xtream, we would need additional fields (username, password)
        // For now, create an empty playlist
        playlist = Playlist(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: _nameController.text,
          url: _urlController.text,
          type: _selectedType,
          channels: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }

      await StorageService.addPlaylist(playlist);
      widget.onPlaylistAdded(playlist);
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playlist "${playlist.name}" added successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding playlist: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _urlController.dispose();
    super.dispose();
  }
}

// Placeholder for ChannelListScreen
class ChannelListScreen extends StatelessWidget {
  final Playlist playlist;

  const ChannelListScreen({super.key, required this.playlist});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(playlist.name),
      ),
      body: playlist.channels.isEmpty
          ? const Center(
              child: Text('No channels available'),
            )
          : ListView.builder(
              itemCount: playlist.channels.length,
              itemBuilder: (context, index) {
                final channel = playlist.channels[index];
                return ListTile(
                  leading: channel.logo != null
                      ? Image.network(
                          channel.logo!,
                          width: 48,
                          height: 48,
                          errorBuilder: (context, error, stackTrace) =>
                              const Icon(Icons.tv),
                        )
                      : const Icon(Icons.tv),
                  title: Text(channel.name),
                  subtitle: Text(channel.group ?? 'No category'),
                  onTap: () {
                    // Navigate to video player with playlist support
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => VideoPlayerScreen(
                          channel: channel,
                          playlist: playlist.channels,
                          currentIndex: index,
                        ),
                      ),
                    );
                  },
                );
              },
            ),
    );
  }
}


