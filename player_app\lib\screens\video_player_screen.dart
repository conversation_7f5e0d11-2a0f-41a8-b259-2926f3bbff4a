import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import '../models/models.dart';

class VideoPlayerScreen extends StatefulWidget {
  final Channel channel;
  final List<Channel>? playlist;
  final int? currentIndex;

  const VideoPlayerScreen({
    super.key,
    required this.channel,
    this.playlist,
    this.currentIndex,
  });

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  late Channel _currentChannel;
  int? _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentChannel = widget.channel;
    _currentIndex = widget.currentIndex;
    _initializePlayer();
    
    // Set preferred orientations for video playback
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
      DeviceOrientation.portraitUp,
    ]);
  }

  Future<void> _initializePlayer() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      // Dispose previous controllers
      await _disposeControllers();

      // Create new video player controller
      _videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(_currentChannel.url),
      );

      await _videoPlayerController!.initialize();

      // Create Chewie controller with custom controls
      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: true,
        looping: false,
        allowFullScreen: true,
        allowMuting: true,
        showControls: true,
        materialProgressColors: ChewieProgressColors(
          playedColor: Theme.of(context).colorScheme.primary,
          handleColor: Theme.of(context).colorScheme.primary,
          backgroundColor: Colors.grey,
          bufferedColor: Colors.grey.shade300,
        ),
        placeholder: Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.red,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(
                  'Playback Error',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _initializePlayer,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        },
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _disposeControllers() async {
    await _chewieController?.dispose();
    await _videoPlayerController?.dispose();
    _chewieController = null;
    _videoPlayerController = null;
  }

  void _playNextChannel() {
    if (widget.playlist != null && _currentIndex != null) {
      final nextIndex = (_currentIndex! + 1) % widget.playlist!.length;
      _switchToChannel(widget.playlist![nextIndex], nextIndex);
    }
  }

  void _playPreviousChannel() {
    if (widget.playlist != null && _currentIndex != null) {
      final prevIndex = (_currentIndex! - 1 + widget.playlist!.length) % widget.playlist!.length;
      _switchToChannel(widget.playlist![prevIndex], prevIndex);
    }
  }

  void _switchToChannel(Channel channel, int index) {
    setState(() {
      _currentChannel = channel;
      _currentIndex = index;
    });
    _initializePlayer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black.withOpacity(0.7),
        title: Text(
          _currentChannel.name,
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (widget.playlist != null && widget.playlist!.length > 1) ...[
            IconButton(
              icon: const Icon(Icons.skip_previous),
              onPressed: _playPreviousChannel,
            ),
            IconButton(
              icon: const Icon(Icons.skip_next),
              onPressed: _playNextChannel,
            ),
          ],
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showChannelInfo,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading stream...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load stream',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Unknown error',
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializePlayer,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_chewieController != null) {
      return Chewie(controller: _chewieController!);
    }

    return const Center(
      child: Text(
        'No video available',
        style: TextStyle(color: Colors.white),
      ),
    );
  }

  void _showChannelInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_currentChannel.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_currentChannel.description != null) ...[
              const Text('Description:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(_currentChannel.description!),
              const SizedBox(height: 8),
            ],
            if (_currentChannel.group != null) ...[
              const Text('Category:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(_currentChannel.group!),
              const SizedBox(height: 8),
            ],
            const Text('Type:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(_currentChannel.type.name.toUpperCase()),
            const SizedBox(height: 8),
            const Text('URL:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(
              _currentChannel.url,
              style: const TextStyle(fontSize: 12),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // Reset preferred orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    
    _disposeControllers();
    super.dispose();
  }
}
