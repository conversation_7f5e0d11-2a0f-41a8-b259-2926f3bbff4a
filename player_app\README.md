# IPTV Player - Flutter Mobile App

A cross-platform IPTV player built with Flutter that supports both M3U playlists and Xtream Codes streaming services.

## 🚀 Features

### 📺 Streaming Support
- **M3U/M3U8 Playlists** - Load from URLs or local files
- **Xtream Codes API** - Full support for Xtream providers
- **Live TV, Movies & Series** - All content types supported
- **Channel Categories** - Organized content browsing

### 📱 Mobile-Optimized
- **File Selection** - Pick M3U files from device storage
- **Touch Controls** - Optimized for mobile interaction
- **Orientation Support** - Landscape mode for video playback
- **Offline Fallback** - Local M3U files when URLs fail

### 🎬 Video Player
- **Full-Screen Playback** - Immersive viewing experience
- **Custom Controls** - Play, pause, seek, volume
- **Channel Switching** - Previous/Next channel navigation
- **Error Handling** - Automatic retry and fallback options

### ⚙️ Configuration
- **Persistent Storage** - Save playlists and settings
- **Multiple Accounts** - Manage multiple Xtream providers
- **Quality Settings** - Video quality and buffer size control
- **Auto-Play Options** - Customize playback behavior

## 📋 How to Use

### Adding M3U Playlists

#### Option 1: From URL
1. Tap the **+** button on the home screen
2. Select **URL** option
3. Enter playlist name and M3U URL
4. Tap **Add** to import channels

#### Option 2: From Local File (Mobile)
1. Tap the **+** button on the home screen
2. Select **File** option
3. Enter playlist name
4. Tap **Select M3U File** to browse device storage
5. Choose your M3U file and tap **Add**

### Adding Xtream Codes Accounts
1. Go to **Settings** tab
2. Tap **Manage Xtream Accounts**
3. Tap **+** to add new account
4. Enter server URL, username, and password
5. Tap **Test Connection** to verify
6. Save the account

### Watching Content
1. Select a playlist from the home screen
2. Browse channels by category
3. Tap a channel to start playback
4. Use player controls for navigation
5. Swipe or use buttons to switch channels
