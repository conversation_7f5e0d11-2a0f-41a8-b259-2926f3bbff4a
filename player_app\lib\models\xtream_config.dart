class XtreamConfig {
  final String id;
  final String name;
  final String serverUrl;
  final String username;
  final String password;
  final DateTime createdAt;
  final DateTime lastUsed;

  XtreamConfig({
    required this.id,
    required this.name,
    required this.serverUrl,
    required this.username,
    required this.password,
    required this.createdAt,
    required this.lastUsed,
  });

  factory XtreamConfig.fromJson(Map<String, dynamic> json) {
    return XtreamConfig(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      serverUrl: json['serverUrl'] ?? '',
      username: json['username'] ?? '',
      password: json['password'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastUsed: DateTime.parse(json['lastUsed'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'serverUrl': serverUrl,
      'username': username,
      'password': password,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
    };
  }

  XtreamConfig copyWith({
    String? id,
    String? name,
    String? serverUrl,
    String? username,
    String? password,
    DateTime? createdAt,
    DateTime? lastUsed,
  }) {
    return XtreamConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      serverUrl: serverUrl ?? this.serverUrl,
      username: username ?? this.username,
      password: password ?? this.password,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
    );
  }

  String get baseApiUrl => '$serverUrl/player_api.php';

  Map<String, String> get authParams => {
        'username': username,
        'password': password,
      };

  @override
  String toString() {
    return 'XtreamConfig(id: $id, name: $name, serverUrl: $serverUrl, username: $username)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is XtreamConfig && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
